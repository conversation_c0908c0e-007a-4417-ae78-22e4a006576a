{"root": true, "env": {"browser": true, "es2021": true, "node": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "settings": {"react": {"version": "detect"}}, "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn"}, "ignorePatterns": ["node_modules/", "dist/", "build/", "*.config.js", "*.config.ts", "coverage/"]}