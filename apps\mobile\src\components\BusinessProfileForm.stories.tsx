import type { Meta, StoryObj } from '@storybook/react';
import BusinessProfileForm from './BusinessProfileForm';
import { action } from '@storybook/addon-actions';

const meta: Meta<typeof BusinessProfileForm> = {
  title: 'Components/BusinessProfileForm',
  component: BusinessProfileForm,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A form component for creating and editing business profiles. Optimized for mobile use with Ionic components and React Hook Form validation.',
      },
    },
  },
  argTypes: {
    onSubmit: { action: 'submitted' },
    onCancel: { action: 'cancelled' },
    loading: {
      control: 'boolean',
      description: 'Shows loading state with spinner',
    },
    error: {
      control: 'text',
      description: 'Error message to display',
    },
    isEdit: {
      control: 'boolean',
      description: 'Whether this is an edit form (true) or create form (false)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof BusinessProfileForm>;

// Default create form
export const CreateForm: Story = {
  args: {
    onSubmit: action('onSubmit'),
    loading: false,
    error: null,
    isEdit: false,
  },
};

// Edit form with initial data
export const EditForm: Story = {
  args: {
    onSubmit: action('onSubmit'),
    onCancel: action('onCancel'),
    loading: false,
    error: null,
    isEdit: true,
    initialData: {
      company_name: 'Strata Compliance Ltd',
      trading_name: 'Strata',
    },
  },
};

// Loading state
export const LoadingState: Story = {
  args: {
    onSubmit: action('onSubmit'),
    onCancel: action('onCancel'),
    loading: true,
    error: null,
    isEdit: false,
    initialData: {
      company_name: 'Strata Compliance Ltd',
      trading_name: 'Strata',
    },
  },
};

// Error state
export const ErrorState: Story = {
  args: {
    onSubmit: action('onSubmit'),
    onCancel: action('onCancel'),
    loading: false,
    error: 'Failed to save business profile. Please try again.',
    isEdit: true,
    initialData: {
      company_name: 'Strata Compliance Ltd',
      trading_name: 'Strata',
    },
  },
};

// Form with cancel button
export const WithCancelButton: Story = {
  args: {
    onSubmit: action('onSubmit'),
    onCancel: action('onCancel'),
    loading: false,
    error: null,
    isEdit: false,
  },
};

// Minimal form (no cancel, no initial data)
export const MinimalForm: Story = {
  args: {
    onSubmit: action('onSubmit'),
    loading: false,
    error: null,
    isEdit: false,
  },
};
