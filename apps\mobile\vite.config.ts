import react from "@vitejs/plugin-react";
import { resolve } from "path";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  envDir: resolve(__dirname, "../../"), // Look for .env files in project root
  server: {
    port: 5173,
    host: true,
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
      "@strata/core": resolve(__dirname, "../../packages/core/src"),
      "@strata/ui": resolve(__dirname, "../../packages/ui/src"),
      "@strata/auth": resolve(__dirname, "../../packages/auth/src"),
    },
  },
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./test/setup.ts"],
  },
});
