import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import BusinessProfileForm from './BusinessProfileForm';

// Mock Ionic React components
vi.mock('@ionic/react', () => ({
  IonButton: ({ children, onClick, disabled, type, ...props }: React.ComponentProps<'button'>) => (
    <button onClick={onClick} disabled={disabled} type={type} {...props}>
      {children}
    </button>
  ),
  IonInput: ({ value, onIonInput, onIonBlur, placeholder, maxlength, ...props }: React.ComponentProps<'input'> & {
    onIonInput?: (e: { detail: { value: string } }) => void;
  }) => (
    <input
      value={value || ''}
      onChange={(e) => {
        onIonInput?.({ detail: { value: e.target.value } });
      }}
      onInput={(e) => {
        onIonInput?.({ detail: { value: (e.target as HTMLInputElement).value } });
      }}
      onBlur={onIonBlur}
      placeholder={placeholder}
      maxLength={maxlength}
      {...props}
    />
  ),
  IonItem: ({ children }: { children: React.ReactNode }) => <div className="ion-item">{children}</div>,
  IonLabel: ({ children }: { children: React.ReactNode }) => <label>{children}</label>,
  IonText: ({ children, color }: { children: React.ReactNode; color?: string }) => <span className={`ion-text-${color}`}>{children}</span>,
  IonSpinner: ({ name }: { name?: string }) => <div className={`ion-spinner-${name}`}>Loading...</div>,
}));

// Mock logging
vi.mock('../lib/logging', () => ({
  debugAuth: vi.fn(),
}));

describe('BusinessProfileForm', () => {
  const mockOnSubmit = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render form with required fields', () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} />);

      expect(screen.getByPlaceholderText('Enter your company name')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter trading name if different')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create profile/i })).toBeInTheDocument();
    });

    it('should render with initial data when provided', () => {
      const initialData = {
        company_name: 'Test Company Ltd',
        trading_name: 'Test Trading',
      };

      render(<BusinessProfileForm onSubmit={mockOnSubmit} initialData={initialData} />);

      expect(screen.getByDisplayValue('Test Company Ltd')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Trading')).toBeInTheDocument();
    });

    it('should show edit mode when isEdit is true', () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} isEdit={true} />);

      expect(screen.getByRole('button', { name: /update profile/i })).toBeInTheDocument();
    });

    it('should show cancel button when onCancel is provided', () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    it('should show loading state', () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} loading={true} />);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
      // When loading, the button text is replaced by spinner, so we check for disabled state
      const submitButton = screen.getByRole('button', { name: /loading/i });
      expect(submitButton).toBeDisabled();
    });

    it('should display error message', () => {
      const errorMessage = 'Something went wrong';
      render(<BusinessProfileForm onSubmit={mockOnSubmit} error={errorMessage} />);

      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should validate required company name', async () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} />);

      const submitButton = screen.getByRole('button', { name: /create profile/i });

      // Submit button should be disabled initially (form is invalid)
      expect(submitButton).toBeDisabled();

      // Try to submit the form without filling required field
      fireEvent.click(submitButton);

      // The form should still be invalid and button disabled
      expect(submitButton).toBeDisabled();
    });

    it('should validate company name length', async () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} />);

      const companyNameInput = screen.getByPlaceholderText('Enter your company name');

      // Test minimum length
      fireEvent.change(companyNameInput, { target: { value: 'A' } });
      fireEvent.blur(companyNameInput);

      await waitFor(() => {
        expect(screen.getByText('Company name must be at least 2 characters')).toBeInTheDocument();
      });

      // Test maximum length
      const longName = 'A'.repeat(101);
      fireEvent.change(companyNameInput, { target: { value: longName } });
      fireEvent.blur(companyNameInput);

      await waitFor(() => {
        expect(screen.getByText('Company name must be less than 100 characters')).toBeInTheDocument();
      });
    });

    it('should validate company name format', async () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} />);

      const companyNameInput = screen.getByPlaceholderText('Enter your company name');

      // Test invalid characters
      fireEvent.change(companyNameInput, { target: { value: 'Company@#$%' } });
      fireEvent.blur(companyNameInput);

      await waitFor(() => {
        expect(screen.getByText('Company name contains invalid characters')).toBeInTheDocument();
      });
    });

    it('should allow valid company names', async () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} />);

      const companyNameInput = screen.getByPlaceholderText('Enter your company name');
      const submitButton = screen.getByRole('button', { name: /create profile/i });

      // Test valid company name
      fireEvent.change(companyNameInput, { target: { value: 'Test Company Ltd.' } });
      fireEvent.blur(companyNameInput);

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
      });

      // Should not show any validation error messages (but label is still there)
      expect(screen.queryByText('Company name is required')).not.toBeInTheDocument();
      expect(screen.queryByText('Company name must be at least 2 characters')).not.toBeInTheDocument();
      expect(screen.queryByText('Company name contains invalid characters')).not.toBeInTheDocument();
    });

    it('should validate optional trading name', async () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} />);

      const tradingNameInput = screen.getByPlaceholderText('Enter trading name if different');

      // Test maximum length
      const longName = 'A'.repeat(101);
      fireEvent.change(tradingNameInput, { target: { value: longName } });
      fireEvent.blur(tradingNameInput);

      await waitFor(() => {
        expect(screen.getByText('Trading name must be less than 100 characters')).toBeInTheDocument();
      });

      // Test invalid characters
      fireEvent.change(tradingNameInput, { target: { value: 'Trading@#$%' } });
      fireEvent.blur(tradingNameInput);

      await waitFor(() => {
        expect(screen.getByText('Trading name contains invalid characters')).toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('should call onSubmit with form data', async () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} />);

      const companyNameInput = screen.getByPlaceholderText('Enter your company name');
      const tradingNameInput = screen.getByPlaceholderText('Enter trading name if different');
      const submitButton = screen.getByRole('button', { name: /create profile/i });

      // Fill form
      fireEvent.change(companyNameInput, { target: { value: 'Test Company Ltd' } });
      fireEvent.change(tradingNameInput, { target: { value: 'Test Trading' } });

      // Wait for validation
      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
      });

      // Submit form
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          company_name: 'Test Company Ltd',
          trading_name: 'Test Trading',
        });
      });
    });

    it('should handle form reset', async () => {
      const initialData = {
        company_name: 'Initial Company',
        trading_name: 'Initial Trading',
      };

      render(<BusinessProfileForm onSubmit={mockOnSubmit} initialData={initialData} />);

      const resetButton = screen.getByRole('button', { name: /reset/i });

      // Reset button should be disabled initially (no changes made)
      expect(resetButton).toBeDisabled();

      // Make a change to enable reset button
      const companyNameInput = screen.getByDisplayValue('Initial Company');
      fireEvent.change(companyNameInput, { target: { value: 'Modified Company' } });

      // Reset button should now be enabled
      await waitFor(() => {
        expect(resetButton).not.toBeDisabled();
      });

      // Click reset
      fireEvent.click(resetButton);

      // Reset button should be disabled again after reset
      await waitFor(() => {
        expect(resetButton).toBeDisabled();
      });
    });

    it('should call onCancel when cancel button is clicked', () => {
      render(<BusinessProfileForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      fireEvent.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });
});
