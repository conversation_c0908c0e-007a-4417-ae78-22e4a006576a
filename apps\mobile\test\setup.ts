import "@testing-library/jest-dom";

// Mock Ionic React components globally
import React from "react";
import { vi } from "vitest";

// Mock the Ionic React module
vi.mock("@ionic/react", () => ({
  IonApp: ({ children }: any) =>
    React.createElement("div", { "data-testid": "ion-app" }, children),
  IonButton: ({
    children,
    onClick,
    disabled,
    type,
    fill,
    expand,
    ...props
  }: any) =>
    React.createElement(
      "button",
      {
        onClick,
        disabled,
        type,
        "data-fill": fill,
        "data-expand": expand,
        ...props,
      },
      children
    ),
  IonInput: ({
    value,
    onIonInput,
    onIonBlur,
    placeholder,
    maxlength,
    counter,
    fill,
    ...props
  }: any) =>
    React.createElement("input", {
      value: value || "",
      onChange: (e: any) => onIonInput?.({ detail: { value: e.target.value } }),
      onBlur: onIonBlur,
      placeholder,
      maxLength: maxlength,
      "data-counter": counter,
      "data-fill": fill,
      ...props,
    }),
  IonItem: ({ children }: any) =>
    React.createElement("div", { className: "ion-item" }, children),
  IonLabel: ({ children, position }: any) =>
    React.createElement("label", { "data-position": position }, children),
  IonText: ({ children, color }: any) =>
    React.createElement("span", { className: `ion-text-${color}` }, children),
  IonSpinner: ({ name }: any) =>
    React.createElement(
      "div",
      { className: `ion-spinner-${name}` },
      "Loading..."
    ),
  IonContent: ({ children, className }: any) =>
    React.createElement(
      "div",
      { className: `ion-content ${className || ""}` },
      children
    ),
  IonPage: ({ children }: any) =>
    React.createElement("div", { className: "ion-page" }, children),
  IonSegment: ({ children, value, onIonChange }: any) =>
    React.createElement(
      "div",
      { className: "ion-segment", "data-value": value, onChange: onIonChange },
      children
    ),
  IonSegmentButton: ({ children, value }: any) =>
    React.createElement(
      "button",
      { className: "ion-segment-button", "data-value": value },
      children
    ),
  IonRouterOutlet: ({ children }: any) =>
    React.createElement("div", { className: "ion-router-outlet" }, children),
  setupIonicReact: vi.fn(),
}));

// Mock Ionic React Router
vi.mock("@ionic/react-router", () => ({
  IonReactRouter: ({ children }: any) =>
    React.createElement("div", { className: "ion-react-router" }, children),
}));

// Mock React Router DOM
vi.mock("react-router-dom", () => ({
  Redirect: ({ to }: any) =>
    React.createElement(
      "div",
      { "data-testid": "redirect", "data-to": to },
      `Redirecting to ${to}`
    ),
  Route: ({ children, path }: any) =>
    React.createElement(
      "div",
      { "data-testid": "route", "data-path": path },
      children
    ),
  Switch: ({ children }: any) =>
    React.createElement("div", { "data-testid": "switch" }, children),
}));

// Mock logging functions
vi.mock("../src/lib/logging", () => ({
  debugAuth: vi.fn(),
  debugApp: vi.fn(),
}));

// Mock Strata packages
vi.mock("@strata/auth", () => ({
  useSupabaseAuth: vi.fn(() => ({
    user: null,
    loading: false,
    error: null,
    signInWithEmail: vi.fn(),
    signInWithMagicLink: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
  })),
}));

vi.mock("@strata/core", () => ({
  // Add any core types or functions that need mocking
}));

vi.mock("@strata/ui", () => ({
  // Add any UI components that need mocking
}));
