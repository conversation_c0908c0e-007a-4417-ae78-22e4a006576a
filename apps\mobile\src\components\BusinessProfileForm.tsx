import {
  IonButton,
  IonInput,
  IonItem,
  IonLabel,
  IonText,
  IonSpinner,
} from '@ionic/react';
import type { BusinessProfile, CreateBusinessProfileRequest, UpdateBusinessProfileRequest } from '@strata/core';
import React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { debugAuth } from '../lib/logging';

interface BusinessProfileFormProps {
  /** Initial data for editing existing profile */
  initialData?: Partial<BusinessProfile>;
  /** Whether the form is in loading state */
  loading?: boolean;
  /** Error message to display */
  error?: string | null;
  /** Called when form is submitted with valid data */
  onSubmit: (data: CreateBusinessProfileRequest | UpdateBusinessProfileRequest) => Promise<void>;
  /** Called when form is cancelled */
  onCancel?: () => void;
  /** Whether this is an edit form (true) or create form (false) */
  isEdit?: boolean;
}

interface FormData {
  company_name: string;
  trading_name?: string;
}

const BusinessProfileForm: React.FC<BusinessProfileFormProps> = ({
  initialData,
  loading = false,
  error,
  onSubmit,
  onCancel,
  isEdit = false,
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isDirty },
    reset,
  } = useForm<FormData>({
    mode: 'onChange', // Real-time validation
    defaultValues: {
      company_name: initialData?.company_name || '',
      trading_name: initialData?.trading_name || '',
    },
  });

  const onFormSubmit = async (data: FormData) => {
    debugAuth('BusinessProfileForm: Submitting form', { data, isEdit });
    try {
      await onSubmit(data);
      debugAuth('BusinessProfileForm: Form submitted successfully');
    } catch (err) {
      debugAuth('BusinessProfileForm: Form submission failed', { error: err });
    }
  };

  const handleReset = () => {
    reset();
    debugAuth('BusinessProfileForm: Form reset');
  };

  const validateCompanyName = (value: string) => {
    if (!value || value.trim().length === 0) {
      return 'Company name is required';
    }
    if (value.trim().length < 2) {
      return 'Company name must be at least 2 characters';
    }
    if (value.length > 100) {
      return 'Company name must be less than 100 characters';
    }
    // Basic validation for business names - allow letters, numbers, spaces, and common business punctuation
    const businessNamePattern = /^[a-zA-Z0-9\s\-&.,()'"]+$/;
    if (!businessNamePattern.test(value)) {
      return 'Company name contains invalid characters';
    }
    return true;
  };

  const validateTradingName = (value?: string) => {
    if (!value || value.trim().length === 0) {
      return true; // Trading name is optional
    }
    if (value.length > 100) {
      return 'Trading name must be less than 100 characters';
    }
    // Same pattern as company name
    const businessNamePattern = /^[a-zA-Z0-9\s\-&.,()'"]+$/;
    if (!businessNamePattern.test(value)) {
      return 'Trading name contains invalid characters';
    }
    return true;
  };

  debugAuth('BusinessProfileForm: Rendering', {
    isEdit,
    loading,
    hasError: !!error,
    isValid,
    isDirty,
  });

  return (
    <form onSubmit={handleSubmit(onFormSubmit)}>
      {/* Company Name Field */}
      <IonItem>
        <IonLabel position="stacked">
          Company Name <IonText color="danger">*</IonText>
        </IonLabel>
        <Controller
          name="company_name"
          control={control}
          rules={{
            validate: validateCompanyName,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <IonInput
              value={value}
              onIonInput={(e) => onChange(e.detail.value!)}
              onIonBlur={onBlur}
              placeholder="Enter your company name"
              maxlength={100}
              counter={true}
              fill="outline"
              aria-describedby={errors.company_name ? 'company-name-error' : undefined}
            />
          )}
        />
      </IonItem>
      {errors.company_name && (
        <IonText color="danger" id="company-name-error">
          <p style={{ margin: '4px 16px', fontSize: '0.875rem' }}>
            {errors.company_name.message}
          </p>
        </IonText>
      )}

      {/* Trading Name Field */}
      <IonItem>
        <IonLabel position="stacked">Trading Name (Optional)</IonLabel>
        <Controller
          name="trading_name"
          control={control}
          rules={{
            validate: validateTradingName,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <IonInput
              value={value}
              onIonInput={(e) => onChange(e.detail.value!)}
              onIonBlur={onBlur}
              placeholder="Enter trading name if different"
              maxlength={100}
              counter={true}
              fill="outline"
              aria-describedby={errors.trading_name ? 'trading-name-error' : undefined}
            />
          )}
        />
      </IonItem>
      {errors.trading_name && (
        <IonText color="danger" id="trading-name-error">
          <p style={{ margin: '4px 16px', fontSize: '0.875rem' }}>
            {errors.trading_name.message}
          </p>
        </IonText>
      )}

      {/* Error Display */}
      {error && (
        <IonText color="danger">
          <p style={{ margin: '16px', textAlign: 'center' }}>{error}</p>
        </IonText>
      )}

      {/* Form Actions */}
      <div style={{ padding: '16px', display: 'flex', gap: '8px' }}>
        {onCancel && (
          <IonButton
            fill="outline"
            expand="block"
            onClick={onCancel}
            disabled={loading}
            style={{ flex: 1 }}
          >
            Cancel
          </IonButton>
        )}
        
        <IonButton
          fill="outline"
          expand="block"
          onClick={handleReset}
          disabled={loading || !isDirty}
          style={{ flex: 1 }}
        >
          Reset
        </IonButton>

        <IonButton
          type="submit"
          expand="block"
          disabled={!isValid || loading}
          style={{ flex: 2 }}
        >
          {loading && <IonSpinner name="crescent" />}
          {!loading && (isEdit ? 'Update Profile' : 'Create Profile')}
        </IonButton>
      </div>
    </form>
  );
};

export default BusinessProfileForm;
