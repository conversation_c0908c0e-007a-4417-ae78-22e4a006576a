# BusinessProfileForm Component

A React component for creating and editing business profiles in the Strata Compliance mobile application. Built with Ionic React components and React Hook Form for optimal mobile performance and user experience.

## Features

- **Mobile-Optimized**: Uses Ionic components for native mobile feel
- **Form Validation**: Real-time validation with React Hook Form
- **Accessibility**: WCAG compliant with proper ARIA labels
- **TypeScript**: Fully typed with comprehensive interfaces
- **Loading States**: Built-in loading and error handling
- **Flexible**: Supports both create and edit modes

## Usage

### Basic Create Form

```tsx
import BusinessProfileForm from './components/BusinessProfileForm';

const handleSubmit = async (data) => {
  // Handle form submission
  console.log('Form data:', data);
};

<BusinessProfileForm
  onSubmit={handleSubmit}
/>
```

### Edit Form with Initial Data

```tsx
const initialData = {
  company_name: 'Strata Compliance Ltd',
  trading_name: 'Strata',
};

const handleSubmit = async (data) => {
  // Handle form update
  await updateBusinessProfile(data);
};

const handleCancel = () => {
  // Handle form cancellation
  navigate('/dashboard');
};

<BusinessProfileForm
  initialData={initialData}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  isEdit={true}
/>
```

### With Loading and Error States

```tsx
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);

const handleSubmit = async (data) => {
  setLoading(true);
  setError(null);
  
  try {
    await saveBusinessProfile(data);
  } catch (err) {
    setError(err.message);
  } finally {
    setLoading(false);
  }
};

<BusinessProfileForm
  onSubmit={handleSubmit}
  loading={loading}
  error={error}
/>
```

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `onSubmit` | `(data: CreateBusinessProfileRequest \| UpdateBusinessProfileRequest) => Promise<void>` | Yes | - | Called when form is submitted with valid data |
| `initialData` | `Partial<BusinessProfile>` | No | `{}` | Initial data for editing existing profile |
| `loading` | `boolean` | No | `false` | Whether the form is in loading state |
| `error` | `string \| null` | No | `null` | Error message to display |
| `onCancel` | `() => void` | No | - | Called when form is cancelled |
| `isEdit` | `boolean` | No | `false` | Whether this is an edit form (true) or create form (false) |

## Form Fields

### Company Name (Required)
- **Type**: Text input
- **Validation**: 
  - Required field
  - Minimum 2 characters
  - Maximum 100 characters
  - Alphanumeric characters, spaces, and common business punctuation allowed
- **Accessibility**: Proper ARIA labels and error announcements

### Trading Name (Optional)
- **Type**: Text input
- **Validation**:
  - Optional field
  - Maximum 100 characters when provided
  - Same character restrictions as company name
- **Accessibility**: Proper ARIA labels

## Validation Rules

The form uses React Hook Form with custom validation functions:

- **Real-time validation**: Validates on change with debouncing
- **Submit validation**: Prevents submission of invalid forms
- **Clear error messages**: User-friendly validation feedback
- **Accessibility**: Screen reader compatible error announcements

## Styling

The component uses Ionic React components with custom styling:

- **Responsive design**: Adapts to different screen sizes
- **Mobile-first**: Optimized for touch interfaces
- **Consistent theming**: Follows Ionic design system
- **Custom spacing**: Proper padding and margins for mobile use

## Accessibility Features

- **ARIA labels**: All form fields have proper labels
- **Error announcements**: Validation errors are announced to screen readers
- **Keyboard navigation**: Full keyboard support
- **Focus management**: Proper focus handling during form interactions
- **High contrast**: Compatible with high contrast modes

## Testing

The component includes comprehensive unit tests covering:

- Component rendering in different states
- Form validation logic
- Form submission handling
- Error and loading states
- Accessibility features

Run tests with:
```bash
pnpm test BusinessProfileForm
```

## Storybook

Interactive documentation and examples are available in Storybook:

```bash
pnpm storybook
```

Stories include:
- Create form
- Edit form with data
- Loading state
- Error state
- Form with cancel button

## Integration

### With Business Profile API

```tsx
import { useBusinessProfile } from '@strata/core';

const BusinessProfilePage = () => {
  const { createProfile, updateProfile, loading, error } = useBusinessProfile();
  
  const handleSubmit = async (data) => {
    if (isEdit) {
      await updateProfile(profileId, data);
    } else {
      await createProfile(data);
    }
  };

  return (
    <BusinessProfileForm
      onSubmit={handleSubmit}
      loading={loading}
      error={error}
      isEdit={!!profileId}
      initialData={existingProfile}
    />
  );
};
```

### With Navigation

```tsx
import { useHistory } from 'react-router-dom';

const BusinessProfileSetup = () => {
  const history = useHistory();
  
  const handleSubmit = async (data) => {
    await saveProfile(data);
    history.push('/dashboard');
  };
  
  const handleCancel = () => {
    history.goBack();
  };

  return (
    <BusinessProfileForm
      onSubmit={handleSubmit}
      onCancel={handleCancel}
    />
  );
};
```

## Performance Considerations

- **React Hook Form**: Minimizes re-renders during form interactions
- **Debounced validation**: Reduces excessive validation calls
- **Lazy loading**: Form components load only when needed
- **Optimized re-rendering**: Uses React.memo where appropriate

## Browser Support

- **iOS Safari**: 12+
- **Android Chrome**: 70+
- **Desktop browsers**: Modern browsers for development/testing
- **Capacitor**: Full native app support
